// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	business_dictionary "nebula/internal/handler/business_dictionary"
	document_library "nebula/internal/handler/document_library"
	externaldocument "nebula/internal/handler/externaldocument"
	file_management_books "nebula/internal/handler/file_management_books"
	internaldocument "nebula/internal/handler/internaldocument"
	signature "nebula/internal/handler/signature"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/business-dictionary/list",
					Handler: business_dictionary.GetBusinessDictionarysHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/business-dictionary/node/create",
					Handler: business_dictionary.CreateBusinessDictionaryNodeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/business-dictionary/node/delete",
					Handler: business_dictionary.DeleteBusinessDictionaryNodeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/business-dictionary/node/list",
					Handler: business_dictionary.GetBusinessDictionaryNodesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/business-dictionary/node/move",
					Handler: business_dictionary.MoveBusinessDictionaryNodeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/business-dictionary/node/relation/count",
					Handler: business_dictionary.GetBusinessDictionaryRelationCountHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/business-dictionary/node/tree",
					Handler: business_dictionary.GetBusinessDictionaryNodeTreeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/business-dictionary/node/update",
					Handler: business_dictionary.UpdateBusinessDictionaryNodeHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/nebula/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/document-library/distribute-info",
					Handler: document_library.GetDistributeDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/distribute/delete",
					Handler: document_library.DeleteDistributeByIDHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/distribute/disposal-detail",
					Handler: document_library.GetDisposalDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/distribute/inventory",
					Handler: document_library.GetDistributeInventoryByIDHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/distribute/list",
					Handler: document_library.GetDistributeListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/distribute/recycle-info",
					Handler: document_library.GetRecycleInfoByDistributeIdHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/document/distribute/user-permissions",
					Handler: document_library.GetDistributeUserPermissionsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/export",
					Handler: document_library.ExportDocumentLibraryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/import",
					Handler: document_library.ImportDocumentLibraryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/loans/add",
					Handler: document_library.AddLoanHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/loans/record/documents",
					Handler: document_library.GetLoanRecordDocumentsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/loans/records",
					Handler: document_library.GetLoanRecordsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/loans/update",
					Handler: document_library.UpdateLoanHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/permission-operation",
					Handler: document_library.PermissionOperationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/document-library/permission/users",
					Handler: document_library.GetDocPermissionUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/document-library/temporary-storage/distribute-info",
					Handler: document_library.TemporaryStorageDistributeInfoHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/nebula/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/workflow/post-callback/disposal",
				Handler: document_library.WorkflowPreCallbackDisposalHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/workflow/pre-callback/distribute",
				Handler: document_library.WorkflowPreCallbackDistributeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/workflow/pre-callback/recycle",
				Handler: document_library.WorkflowPreCallbackRecycleHandler(serverCtx),
			},
		},
		rest.WithPrefix("/nebula/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/external/document",
					Handler: externaldocument.GetExternalDocumentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/external/document/change",
					Handler: externaldocument.ChangeExternalDocumentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/external/document/create",
					Handler: externaldocument.CreateExternalDocumentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/external/documents",
					Handler: externaldocument.GetExternalDocumentsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/external/import/company/plagiarism-check",
					Handler: externaldocument.PlagiarismCheckHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/nebula/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/book/create",
					Handler: file_management_books.CreateBookHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/book/delete",
					Handler: file_management_books.DeleteBookHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/book/import",
					Handler: file_management_books.ImportBookInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/book/list",
					Handler: file_management_books.GetBookListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/book/update",
					Handler: file_management_books.UpdateBookHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/nebula/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/internal/document",
					Handler: internaldocument.GetInternalDocumentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/internal/document/change",
					Handler: internaldocument.ChangeInternalDocumentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/internal/document/create",
					Handler: internaldocument.CreateInternalDocumentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/internal/documents",
					Handler: internaldocument.GetInternalDocumentsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/nebula/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/signature-task",
					Handler: signature.CreateSignatureTaskHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/signature-task/status",
					Handler: signature.GetSignatureTaskStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/signature/upload",
					Handler: signature.UploadSignatureHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/signatures/current",
					Handler: signature.GetCurrentSignatureHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/signatures/history",
					Handler: signature.GetSignatureHistoryHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/nebula/api/v1"),
	)
}
